package middleware

import (
	"context"

	"digital-transformation-api/libs/apps"
	"digital-transformation-api/libs/contexts"
	"digital-transformation-api/libs/gins"

	"github.com/gin-gonic/gin"
)

// RouteContext middleware creates and sets the route context for each request
func RouteContext() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		// Create route context with header information
		rctx := &contexts.RouteContext{
			Header: contexts.Header{
				TraceId: gins.GetIDByKey(apps.TraceID, ctx),
				SpanId:  gins.GetIDByKey(apps.SpanID, ctx),
			},
			Ctx: context.Background(),
		}

		// Set the route context in the Gin context
		ctx.Set(apps.RouteContext, rctx)

		// Continue to next middleware/handler
		ctx.Next()
	}
}
